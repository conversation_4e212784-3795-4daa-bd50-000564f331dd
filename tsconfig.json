{"extends": "astro/tsconfigs/strict", "include": [".astro/types.d.ts", "**/*"], "exclude": ["dist", "node_modules"], "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "react", "verbatimModuleSyntax": true, "allowImportingTsExtensions": false, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/layouts/*": ["./src/layouts/*"], "@/pages/*": ["./src/pages/*"], "@/styles/*": ["./src/styles/*"], "@/assets/*": ["./src/assets/*"], "@/scripts/*": ["./src/scripts/*"], "@components/*": ["./src/components/*"], "@layouts/*": ["./src/layouts/*"], "@pages/*": ["./src/pages/*"], "@styles/*": ["./src/styles/*"], "@assets/*": ["./src/assets/*"], "@scripts/*": ["./src/scripts/*"]}}}